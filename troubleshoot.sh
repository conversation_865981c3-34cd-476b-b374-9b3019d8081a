#!/bin/bash
# Troubleshooting script

echo "🔍 Checking for required files..."
[[ -f "src/../ship.jpg" ]] && echo "✅ ship.jpg found" || echo "❌ ship.jpg missing"
[[ -f "src/../kernel.txt" ]] && echo "✅ kernel.txt found" || echo "❌ kernel.txt missing"
[[ -f "src/../config.txt" ]] && echo "✅ config.txt found" || echo "❌ config.txt missing"

for i in {0..3}; do
  [[ -f "src/../config${i}.txt" ]] && echo "✅ config${i}.txt found" || echo "❌ config${i}.txt missing"
done

echo "🔍 Checking for running processes..."
pgrep -f "rmiregistry 13190" && echo "✅ RMI Registry running" || echo "❌ RMI Registry not running"
pgrep -f "Classes.TaskSchedulerServer" && echo "✅ Task Scheduler running" || echo "❌ Task Scheduler not running"
pgrep -f "Classes.SlaveTask" && echo "✅ Slave Tasks running" || echo "❌ No Slave Tasks running"
pgrep -f "Classes.Tasks" && echo "✅ GUI Client running" || echo "❌ GUI Client not running"

echo "🔍 Checking log files for errors..."
for i in {0..3}; do
  if [[ -f "src/slave${i}.log" ]]; then
    echo "Errors in slave${i}.log:"
    grep -i "error\|exception" "src/slave${i}.log" | tail -5
  fi
done

if [[ -f "src/server.log" ]]; then
  echo "Errors in server.log:"
  grep -i "error\|exception" "src/server.log" | tail -5
fi

if [[ -f "src/gui.log" ]]; then
  echo "Errors in gui.log:"
  grep -i "error\|exception" "src/gui.log" | tail -5
fi