#!/bin/bash
# 🛑 Stop All Distributed Image Processing System Components

echo "🛑 Stopping Distributed Image Processing System..."
echo "================================================="

# Kill all Java processes related to the system
echo "🧹 Stopping all system components..."

# Stop in reverse order for clean shutdown
pkill -f "Classes.Tasks" 2>/dev/null && echo "   ✅ GUI Client stopped" || echo "   ℹ️  GUI Client not running"
sleep 1

pkill -f "Classes.TaskSchedulerServer" 2>/dev/null && echo "   ✅ Task Scheduler Server stopped" || echo "   ℹ️  Task Scheduler Server not running"
sleep 1

pkill -f "Classes.SlaveTask" 2>/dev/null && echo "   ✅ Slave Workers stopped" || echo "   ℹ️  Slave Workers not running"
sleep 1

pkill -f "rmiregistry 13190" 2>/dev/null && echo "   ✅ RMI Registry stopped" || echo "   ℹ️  RMI Registry not running"

# Wait a moment for processes to terminate
sleep 2

# Double-check and force kill if necessary
REMAINING=$(ps aux | grep -E "(Classes\.|rmiregistry 13190)" | grep -v grep | wc -l)
if [ $REMAINING -gt 0 ]; then
    echo "🔄 Force stopping remaining processes..."
    pkill -9 -f "Classes\." 2>/dev/null
    pkill -9 -f "rmiregistry 13190" 2>/dev/null
    sleep 1
fi

echo ""
echo "🎉 All components stopped successfully!"
echo ""
echo "🚀 To restart the system, run: ./start_all.sh"
