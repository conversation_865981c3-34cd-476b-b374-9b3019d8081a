# 🖼️ Distributed Image Processing System - Setup Instructions

## 📋 Prerequisites
- **Java JDK 8 or higher** (check with `java -version` and `javac -version`)
- **Sample files on Desktop:**
  - An image file (e.g., `ship.jpg`)
  - A kernel file (e.g., `kernel.txt`) for convolution operations

## 🚀 Quick Setup (7 Steps)

### Step 1: Compile the Project
```bash
cd Java_Task_Scheduling/src
javac Classes/*.java
```

### Step 2: Open 7 Terminal Windows
You need to run 7 different components simultaneously.

### Step 3: Start RMI Registry (Terminal 1)
```bash
cd Java_Task_Scheduling/src
rmiregistry 13190
```
*Keep this terminal running - you should see no output*

### Step 4: Start Task Scheduler Server (Terminal 2)
```bash
cd Java_Task_Scheduling/src
java Classes.TaskSchedulerServer
```
*Wait for "Task Scheduler Server started" message*

### Step 5: Start 4 Slaves (Terminals 3-6)
**Terminal 3:**
```bash
cd Java_Task_Scheduling/src
java Classes.Slave 13191
```

**Terminal 4:**
```bash
cd Java_Task_Scheduling/src
java Classes.Slave 13192
```

**Terminal 5:**
```bash
cd Java_Task_Scheduling/src
java Classes.Slave 13193
```

**Terminal 6:**
```bash
cd Java_Task_Scheduling/src
java Classes.Slave 13194
```
*Each slave should show "start reading" message*

### Step 6: Start GUI Client (Terminal 7)
```bash
cd Java_Task_Scheduling/src
java Classes.Tasks
```
*A GUI window will open*

## 🎯 How to Use

### For Image Filtering:
1. Select **"Filters"** radio button
2. Choose filter from dropdown: **Gray**, **Blue**, **Red**, or **Green**
3. Enter image filename in "Choose image" field (e.g., `ship.jpg`)
4. Click **"Display Result"**
5. Processed image will appear in a new window

### For Convolution:
1. Select **"Convolution"** radio button
2. Enter kernel filename (e.g., `kernel.txt`)
3. Enter image filename (e.g., `ship.jpg`)
4. Click **"Display Result"**
5. Convolved image will appear in a new window

## ✅ System Status Check

**Working correctly when you see:**
- RMI Registry: No output (silent)
- Server: "Task Scheduler Server started"
- Slaves: "start reading" messages
- GUI: Window opens with dropdown and buttons
- Processing: Slaves show filter names and "sending...." messages

## 🔧 Troubleshooting

**If GUI doesn't open:**
- Check Java version: `java -version`
- Ensure all previous steps completed successfully

**If "No result" or errors:**
- Verify image file exists on Desktop
- Check all 6 background processes are running
- Restart in order: RMI → Server → Slaves → GUI

**If dropdown doesn't work:**
- Close GUI and restart: `java Classes.Tasks`
- Ensure you're clicking the dropdown arrow

**Common Issues:**
- **Port conflicts:** Change port numbers if 13190-13194 are in use
- **File not found:** Ensure image files are exactly on Desktop
- **Permission errors:** Run with appropriate file permissions

## 📁 File Structure
```
Java_Task_Scheduling/
├── src/Classes/
│   ├── *.java (source files)
│   ├── *.class (compiled files)
├── ship.jpg (sample image)
├── kernel.txt (sample kernel)
└── SETUP_INSTRUCTIONS.md (this file)
```

## 🎉 Success!
When working, you'll see:
- Different colored filters applied to your images
- Real-time processing across 4 slave nodes
- Distributed task scheduling in action

**Enjoy your distributed image processing system!** 🚀

---
*For questions or issues, check that all 7 components are running and files are on Desktop.*
