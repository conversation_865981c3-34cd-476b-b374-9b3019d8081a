<?xml version="1.0" encoding="UTF-8"?>
<project-private xmlns="http://www.netbeans.org/ns/project-private/1">
    <editor-bookmarks xmlns="http://www.netbeans.org/ns/editor-bookmarks/2" lastBookmarkId="0"/>
    <open-files xmlns="http://www.netbeans.org/ns/projectui-open-files/2">
        <group>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/TaskSchedulerInterface.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/TaskResult.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/FilterTask.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/WorkerThread.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/Taskqueue.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/convolutionresult.jpg</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/Task.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/TaskSchedulerClient.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/TaskScheduler.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/TaskSchedulerServer.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/filterresult.jpg</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/SlaveTask.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/CovolutionTask.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/MatrixTask.java</file>
            <file>file:/C:/Users/<USER>/Documents/NetBeansProjects/End_of_module/src/Classes/Tasks.java</file>
        </group>
    </open-files>
</project-private>
