#!/bin/bash
# Manual testing script for debugging

echo "🔧 Manual Testing Script"
cd "/Users/<USER>/Semester 6/dsNand<PERSON>/Java_Task_Scheduling/src"

echo "1. Starting RMI Registry..."
rmiregistry 13190 &
RMI_PID=$!
echo "   RMI PID: $RMI_PID"
sleep 2

echo "2. Starting Slave 0..."
java Classes.SlaveTask config0.txt &
SLAVE0_PID=$!
echo "   Slave 0 PID: $SLAVE0_PID"
sleep 2

echo "3. Checking if slave is listening on port 9000..."
if lsof -i :9000 >/dev/null 2>&1; then
    echo "   ✅ Slave 0 is listening on port 9000"
else
    echo "   ❌ Slave 0 is NOT listening on port 9000"
fi

echo "4. Starting Server..."
java Classes.TaskSchedulerServer config.txt &
SERVER_PID=$!
echo "   Server PID: $SERVER_PID"
sleep 3

echo "5. Checking processes..."
jps | grep -E "(SlaveTask|TaskSchedulerServer|RegistryImpl)"

echo "6. Starting GUI..."
java Classes.Tasks &
GUI_PID=$!
echo "   GUI PID: $GUI_PID"

echo ""
echo "All PIDs:"
echo "RMI: $RMI_PID, Slave0: $SLAVE0_PID, Server: $SERVER_PID, GUI: $GUI_PID"
echo ""
echo "Press Enter to stop all processes..."
read
pkill -f "Classes\." && pkill -f "rmiregistry"
echo "Stopped."
