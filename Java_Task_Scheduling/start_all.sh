#!/bin/bash
# 🚀 Distributed Image Processing System - One-Command Startup Script

echo "🚀 Starting Distributed Image Processing System..."
echo "=================================================="

cd "$(dirname "$0")/src" || exit 1

DESKTOP="$HOME/Desktop"
echo "📁 Copying required files to Desktop..."
[[ -f "../ship.jpg" ]] && cp ../ship.jpg "$DESKTOP/"
[[ -f "../kernel.txt" ]] && cp ../kernel.txt "$DESKTOP/"
if [[ -f "../Xtra/config.txt" ]]; then
    cp ../Xtra/config*.txt "$DESKTOP/"
elif [[ -f "../config.txt" ]]; then
    cp ../config*.txt "$DESKTOP/"
fi
echo "✅ Files copied to Desktop"

echo "🧹 Cleaning up existing processes..."
pkill -f "rmiregistry 13190" 2>/dev/null || true
pkill -f "Classes.TaskSchedulerServer" 2>/dev/null || true
pkill -f "Classes.SlaveTask" 2>/dev/null || true
pkill -f "Classes.Tasks" 2>/dev/null || true
sleep 2

echo "🔨 Compiling Java files..."
javac Classes/*.java
if [ $? -ne 0 ]; then
    echo "❌ Compilation failed!"
    exit 1
fi
echo "✅ Compilation successful!"

echo "🌐 Starting RMI Registry on port 13190..."
rmiregistry 13190 &
RMI_PID=$!
sleep 5  # longer wait to prevent race condition

echo "👷 Starting Slave Workers..."

for i in {0..3}; do
    CONFIG="config${i}.txt"
    LOGFILE="slave${i}.log"
    PORT=$((9000 + i))

    if [[ ! -f $CONFIG ]]; then
        echo "❌ Missing $CONFIG, cannot start Slave $((i+1))"
        continue
    fi

    echo "   Starting Slave $((i+1)) (port $PORT)..."
    java Classes.SlaveTask "$CONFIG" > "$LOGFILE" 2>&1 &
    PID=$!
    eval "SLAVE${i}_PID=$PID"
    sleep 3

    if ! ps -p $PID > /dev/null; then
        echo "❌ Slave $((i+1)) crashed! See $LOGFILE for details:"
        cat "$LOGFILE"
    fi
done

echo "🖥️  Starting Task Scheduler Server..."
java Classes.TaskSchedulerServer config.txt > server.log 2>&1 &
SERVER_PID=$!
sleep 5

echo "🖼️  Starting GUI Client..."
java Classes.Tasks > gui.log 2>&1 &
GUI_PID=$!
sleep 3

echo ""
echo "🎉 System Started Successfully!"
echo "================================"
echo "✅ RMI Registry: Running (PID: $RMI_PID)"
for i in {0..3}; do
    PID_VAR="SLAVE${i}_PID"
    PID_VAL=${!PID_VAR}
    echo "✅ Slave $((i+1)): Running (PID: $PID_VAL)"
done
echo "✅ Task Scheduler: Running (PID: $SERVER_PID)"
echo "✅ GUI Client: Running (PID: $GUI_PID)"
echo ""
echo "📋 Usage:"
echo "   • GUI window should be open"
echo "   • Select 'Filters' and choose a color filter (Gray/Blue/Red/Green)"
echo "   • Make sure 'ship.jpg' is on your Desktop"
echo "   • Click 'Display Result' to process images"
echo ""
echo "🛑 To stop all processes, run: ./stop_all.sh"
echo ""
echo "🎯 System ready for distributed image processing!"
