<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <NonVisualComponents>
    <Component class="javax.swing.ButtonGroup" name="buttonGroup1">
    </Component>
  </NonVisualComponents>
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="99" pref="99" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jButton1" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="radio1" alignment="0" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace min="-2" pref="31" max="-2" attributes="0"/>
                      <Component id="jScrollPane1" min="-2" pref="145" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <Component id="jLabel5" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="F4" min="-2" pref="110" max="-2" attributes="0"/>
                  </Group>
                  <Component id="radio2" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
                      <EmptySpace type="unrelated" max="-2" attributes="0"/>
                      <Component id="F2" min="-2" pref="110" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace pref="486" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <EmptySpace min="-2" pref="23" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="F4" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="23" max="-2" attributes="0"/>
              <Component id="radio2" min="-2" max="-2" attributes="0"/>
              <EmptySpace type="unrelated" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="F2" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="28" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="1" attributes="0">
                  <Group type="102" attributes="0">
                      <Component id="radio1" min="-2" max="-2" attributes="0"/>
                      <EmptySpace type="unrelated" max="-2" attributes="0"/>
                      <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Component id="jScrollPane1" min="-2" pref="24" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="27" max="-2" attributes="0"/>
              <Component id="jButton1" min="-2" max="-2" attributes="0"/>
              <EmptySpace pref="91" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JRadioButton" name="radio2">
      <Properties>
        <Property name="buttonGroup" type="javax.swing.ButtonGroup" editor="org.netbeans.modules.form.RADComponent$ButtonGroupPropertyEditor">
          <ComponentRef name="buttonGroup1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Convolution"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="radio2ActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JRadioButton" name="radio1">
      <Properties>
        <Property name="buttonGroup" type="javax.swing.ButtonGroup" editor="org.netbeans.modules.form.RADComponent$ButtonGroupPropertyEditor">
          <ComponentRef name="buttonGroup1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Filters"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="radio1ActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="text" type="java.lang.String" value="kernel :"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="F2">
      <Properties>
        <Property name="text" type="java.lang.String" value="kernel.txt"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="F2ActionPerformed"/>
      </Events>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JList" name="list1">
          <Properties>
            <Property name="model" type="javax.swing.ListModel" editor="org.netbeans.modules.form.editors2.ListModelEditor">
              <StringArray count="4">
                <StringItem index="0" value="Gray"/>
                <StringItem index="1" value="Blue"/>
                <StringItem index="2" value="Red"/>
                <StringItem index="3" value="Green"/>
              </StringArray>
            </Property>
          </Properties>
          <AuxValues>
            <AuxValue name="JavaCodeGenerator_TypeParameters" type="java.lang.String" value="&lt;String&gt;"/>
          </AuxValues>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="text" type="java.lang.String" value="choose flter ;"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="jButton1">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="33" red="0" type="rgb"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Display Result"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton1ActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="text" type="java.lang.String" value="Choose image :"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="F4">
      <Properties>
        <Property name="text" type="java.lang.String" value="ship.jpg"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="F4ActionPerformed"/>
      </Events>
    </Component>
  </SubComponents>
</Form>
