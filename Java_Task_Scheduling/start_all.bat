@echo off
title Distributed Image Processing System
color 0A

echo.
echo 🚀 Starting Distributed Image Processing System...
echo ==================================================
echo.

REM Navigate to source directory (relative to script location)
cd /d "%~dp0src"

REM Check if we're in the right directory
if not exist "Classes" (
    echo ❌ Error: Classes directory not found!
    echo Make sure this script is in the Java_Task_Scheduling folder
    pause
    exit /b 1
)

REM Copy required files to Desktop
echo 📁 Copying required files to Desktop...
set DESKTOP=%USERPROFILE%\Desktop

if exist "..\ship.jpg" (
    copy "..\ship.jpg" "%DESKTOP%\" >nul 2>&1
)
if exist "..\kernel.txt" (
    copy "..\kernel.txt" "%DESKTOP%\" >nul 2>&1
)
if exist "..\Xtra\config.txt" (
    copy "..\Xtra\config*.txt" "%DESKTOP%\" >nul 2>&1
) else if exist "..\config.txt" (
    copy "..\config*.txt" "%DESKTOP%\" >nul 2>&1
)
echo ✅ Files copied to Desktop
echo.

REM Kill any existing processes (cleanup)
echo 🧹 Cleaning up existing processes...
taskkill /f /im rmiregistry.exe >nul 2>&1
taskkill /f /im java.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM Compile all Java files
echo 🔨 Compiling Java files...
javac Classes\*.java
if %errorlevel% neq 0 (
    echo ❌ Compilation failed!
    pause
    exit /b 1
)
echo ✅ Compilation successful!
echo.

REM Start RMI Registry
echo 🌐 Starting RMI Registry on port 13190...
start "RMI Registry" /min rmiregistry 13190
timeout /t 2 /nobreak >nul

REM Start 4 Slave Workers FIRST (they need to be listening before server connects)
echo 👷 Starting Slave Workers...
echo    Starting Slave 1 (port 9000)...
start "Slave Worker 1" java Classes.SlaveTask config0.txt
timeout /t 2 /nobreak >nul

echo    Starting Slave 2 (port 9001)...
start "Slave Worker 2" java Classes.SlaveTask config1.txt
timeout /t 2 /nobreak >nul

echo    Starting Slave 3 (port 9002)...
start "Slave Worker 3" java Classes.SlaveTask config2.txt
timeout /t 2 /nobreak >nul

echo    Starting Slave 4 (port 9003)...
start "Slave Worker 4" java Classes.SlaveTask config3.txt
timeout /t 3 /nobreak >nul

REM Start Task Scheduler Server (after slaves are ready)
echo 🖥️  Starting Task Scheduler Server...
start "Task Scheduler Server" java Classes.TaskSchedulerServer config.txt
timeout /t 5 /nobreak >nul

REM Start GUI Client
echo 🖼️  Starting GUI Client...
start "GUI Client" java Classes.Tasks

REM Wait a moment for everything to initialize
timeout /t 3 /nobreak >nul

echo.
echo 🎉 System Started Successfully!
echo ================================
echo ✅ RMI Registry: Running
echo ✅ Slave 1: Running
echo ✅ Slave 2: Running  
echo ✅ Slave 3: Running
echo ✅ Slave 4: Running
echo ✅ Task Scheduler: Running
echo ✅ GUI Client: Running
echo.
echo 📋 Usage:
echo    • GUI window should be open
echo    • Select 'Filters' and choose a color filter (Gray/Blue/Red/Green)
echo    • Make sure 'ship.jpg' is on your Desktop
echo    • Click 'Display Result' to process images
echo.
echo 🛑 To stop all processes: Close all Java windows or run stop_all.bat
echo.
echo 🎯 System ready for distributed image processing!
echo.
pause
